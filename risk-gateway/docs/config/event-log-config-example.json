{"ApiVerify": {"fields": [{"name": "sessionId", "expression": "$.params.sessionId", "type": "jsonpath"}, {"name": "<PERSON><PERSON><PERSON>", "expression": "$.params.userKey", "type": "jsonpath"}, {"name": "eventCode", "expression": "$.params.eventCode", "type": "jsonpath"}, {"name": "sourceSystem", "expression": "$.params.sourceSystem", "type": "jsonpath"}, {"name": "occurTime", "expression": "$.params.occurTime", "type": "jsonpath"}], "targetKey": "apiVerifyInfo"}, "haoHuanApply": {"fields": [{"name": "userId", "expression": "params.user<PERSON>ey", "type": "mvel"}, {"name": "loan<PERSON>ey", "expression": "params.loanKey", "type": "mvel"}, {"name": "riskScore", "expression": "verifyResult.riskScore", "type": "mvel"}, {"name": "riskLevel", "expression": "verifyResult.riskLevel", "type": "mvel"}, {"name": "processTime", "expression": "params.occurTime", "type": "mvel"}, {"name": "userInfo", "expression": "params.userKey + '_' + params.sourceSystem", "type": "mvel"}], "targetKey": "haoHuanData"}, "loanApply": {"fields": [{"name": "sessionId", "expression": "$.params.sessionId", "type": "jsonpath"}, {"name": "<PERSON><PERSON><PERSON>", "expression": "$.params.userKey", "type": "jsonpath"}, {"name": "loanAmount", "expression": "$.params.loanAmount", "type": "jsonpath"}, {"name": "loanTerm", "expression": "$.params.loanTerm", "type": "jsonpath"}, {"name": "finalDecision", "expression": "verifyResult.finalDecision", "type": "mvel"}, {"name": "riskScore", "expression": "verifyResult.riskScore", "type": "mvel"}], "targetKey": "loanInfo"}, "prepareHaoHuanApply": {"fields": [{"name": "sessionId", "expression": "$.params.sessionId", "type": "jsonpath"}, {"name": "<PERSON><PERSON><PERSON>", "expression": "$.params.userKey", "type": "jsonpath"}, {"name": "preApprovalAmount", "expression": "$.verifyResult.preApprovalAmount", "type": "jsonpath"}, {"name": "preApprovalResult", "expression": "$.verifyResult.preApprovalResult", "type": "jsonpath"}, {"name": "deviceInfo", "expression": "params.deviceId + '_' + params.appVersion", "type": "mvel"}]}, "riskMonitor": {"fields": [{"name": "monitorType", "expression": "$.params.monitorType", "type": "jsonpath"}, {"name": "alertLevel", "expression": "$.verifyResult.alertLevel", "type": "jsonpath"}, {"name": "riskIndicators", "expression": "$.verifyResult.riskIndicators", "type": "jsonpath"}, {"name": "<PERSON><PERSON><PERSON>", "expression": "$.params.userKey", "type": "jsonpath"}], "targetKey": "monitorData"}, "fraudDetection": {"fields": [{"name": "fraudScore", "expression": "verifyResult.fraudScore", "type": "mvel"}, {"name": "fraudRules", "expression": "verifyResult.hitRules", "type": "mvel"}, {"name": "deviceFingerprint", "expression": "params.deviceId", "type": "mvel"}, {"name": "ipLocation", "expression": "params.ip + '_' + params.location", "type": "mvel"}, {"name": "userBeh<PERSON>or", "expression": "$.dataVo.behaviorAnalysis", "type": "jsonpath"}], "targetKey": "fraudInfo"}}