package com.youxin.risk.gateway.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.youxin.apollo.client.NacosClient;
import com.youxin.risk.commons.constants.ApolloNamespace;
import com.youxin.risk.commons.model.Event;
import com.youxin.risk.commons.utils.LoggerProxy;
import org.apache.commons.lang3.StringUtils;
import org.mvel2.MVEL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * 事件日志配置工具类
 * 用于根据Nacos配置提取事件数据并格式化为JSON字符串
 * 
 * <AUTHOR>
 */
public class EventLogConfigUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(EventLogConfigUtil.class);
    
    /**
     * Nacos配置key，用于配置事件维度的日志打印字段
     */
    private static final String EVENT_LOG_CONFIG_KEY = "event.log.config";
    
    /**
     * 根据事件代码和Nacos配置提取事件数据
     * 
     * @param event 事件对象
     * @return 格式化后的JSON字符串，如果没有配置或提取失败则返回空字符串
     */
    public static String extractEventData(Event event) {
        if (event == null) {
            return "";
        }
        
        String eventCode = event.getEventCode();
        if (StringUtils.isBlank(eventCode)) {
            return "";
        }
        
        try {
            // 从Nacos获取配置
            String configString = NacosClient.getByNameSpace(ApolloNamespace.commonSpace, EVENT_LOG_CONFIG_KEY, "");
            if (StringUtils.isBlank(configString)) {
                LoggerProxy.debug("extractEventData", logger, "事件日志配置为空，eventCode={}", eventCode);
                return "";
            }
            
            JSONObject config = JSON.parseObject(configString);
            JSONObject eventConfig = config.getJSONObject(eventCode);
            if (eventConfig == null) {
                LoggerProxy.debug("extractEventData", logger, "未找到事件配置，eventCode={}", eventCode);
                return "";
            }
            
            // 提取字段配置
            JSONArray fields = eventConfig.getJSONArray("fields");
            String targetKey = eventConfig.getString("targetKey");
            
            if (fields == null || fields.isEmpty()) {
                LoggerProxy.debug("extractEventData", logger, "事件字段配置为空，eventCode={}", eventCode);
                return "";
            }
            
            // 提取数据
            Map<String, Object> extractedData = extractFields(event, fields);
            
            if (extractedData.isEmpty()) {
                return "";
            }
            
            // 根据targetKey配置决定返回格式
            if (StringUtils.isNotBlank(targetKey)) {
                Map<String, Object> result = new HashMap<>();
                result.put(targetKey, extractedData);
                return JSON.toJSONString(result);
            } else {
                return JSON.toJSONString(extractedData);
            }
            
        } catch (Exception e) {
            LoggerProxy.warn("extractEventData", logger, "提取事件数据失败，eventCode={}, error={}", eventCode, e.getMessage());
            return "";
        }
    }
    
    /**
     * 根据字段配置提取事件数据
     * 
     * @param event 事件对象
     * @param fields 字段配置数组
     * @return 提取的数据Map
     */
    private static Map<String, Object> extractFields(Event event, JSONArray fields) {
        Map<String, Object> result = new HashMap<>();
        
        for (int i = 0; i < fields.size(); i++) {
            JSONObject fieldConfig = fields.getJSONObject(i);
            if (fieldConfig == null) {
                continue;
            }
            
            String fieldName = fieldConfig.getString("name");
            String expression = fieldConfig.getString("expression");
            String type = fieldConfig.getString("type"); // jsonpath 或 mvel
            
            if (StringUtils.isBlank(fieldName) || StringUtils.isBlank(expression)) {
                LoggerProxy.warn("extractFields", logger, "字段配置不完整，fieldName={}, expression={}", fieldName, expression);
                continue;
            }
            
            try {
                Object value = extractFieldValue(event, expression, type);
                if (value != null) {
                    result.put(fieldName, value);
                }
            } catch (Exception e) {
                LoggerProxy.warn("extractFields", logger, "提取字段值失败，fieldName={}, expression={}, error={}", 
                    fieldName, expression, e.getMessage());
            }
        }
        
        return result;
    }
    
    /**
     * 根据表达式类型提取字段值
     * 
     * @param event 事件对象
     * @param expression 表达式
     * @param type 表达式类型（jsonpath 或 mvel）
     * @return 提取的值
     */
    private static Object extractFieldValue(Event event, String expression, String type) {
        if (StringUtils.isBlank(type)) {
            type = "jsonpath"; // 默认使用jsonpath
        }
        
        if ("mvel".equalsIgnoreCase(type)) {
            return extractWithMvel(event, expression);
        } else {
            return extractWithJsonPath(event, expression);
        }
    }
    
    /**
     * 使用JSONPath表达式提取值
     * 
     * @param event 事件对象
     * @param expression JSONPath表达式
     * @return 提取的值
     */
    private static Object extractWithJsonPath(Event event, String expression) {
        try {
            // 将Event对象转换为JSON字符串，然后使用JSONPath提取
            String eventJson = JSON.toJSONString(event);
            return JSONPath.read(eventJson, expression);
        } catch (Exception e) {
            LoggerProxy.debug("extractWithJsonPath", logger, "JSONPath提取失败，expression={}, error={}", expression, e.getMessage());
            return null;
        }
    }
    
    /**
     * 使用MVEL表达式提取值
     * 
     * @param event 事件对象
     * @param expression MVEL表达式
     * @return 提取的值
     */
    private static Object extractWithMvel(Event event, String expression) {
        try {
            // 创建MVEL上下文
            Map<String, Object> context = new HashMap<>();
            context.put("event", event);
            context.put("params", event.getParams());
            context.put("dataVo", event.getDataVo());
            context.put("verifyResult", event.getVerifyResult());
            
            return MVEL.eval(expression, context);
        } catch (Exception e) {
            LoggerProxy.debug("extractWithMvel", logger, "MVEL提取失败，expression={}, error={}", expression, e.getMessage());
            return null;
        }
    }
}
